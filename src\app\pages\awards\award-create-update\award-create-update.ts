import { Component, DestroyRef, inject, Signal, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { AwardsService } from '@core/services/http/awards';
import { HeaderService } from '@core/services/utils/header';
import { ImageService } from '@core/services/utils/image';
import { MessageService } from '@core/services/utils/message';
import { ModalService } from '@core/services/utils/modal';
import { UploadService, ValidateImages } from '@core/services/utils/upload';
import { FormUtils } from '@core/utils/form';
import { getBase64Async } from '@core/utils/image';
import { CreateUpdateItem } from '@models/entities/create-update-item';
import { crudActionType } from '@models/enums/crud-action-type';
import { currentSectionType } from '@models/enums/current-section';
import { IAward } from '@models/interfaces/award';
import {
  IImageSize,
  ShowUploadListImage,
} from '@models/interfaces/file-upload';
import { log } from 'ng-zorro-antd/core/logger';
import { NzUploadFile, NzUploadXHRArgs } from 'ng-zorro-antd/upload';
import { delay, of, switchMap, tap } from 'rxjs';

enum ValidatorsField {
  IMAGES = 'images',
}

const IMAGE_LIMITS: IImageSize = {
  width: {
    min: 200,
    max: 2000,
  },
  height: {
    min: 200,
    max: 2000,
  },
  size: {
    kilobytes: 16,
    megabytes: 16,
    gigabytes: 16,
  },
};
@Component({
  selector: 'app-award-create-update',
  imports: [FormsModule, ReactiveFormsModule],
  providers: [UploadService],
  templateUrl: './award-create-update.html',
  styleUrl: './award-create-update.less',
})
export class AwardCreateUpdate extends CreateUpdateItem {
  private route = inject(ActivatedRoute);
  private destroyRef = inject(DestroyRef);
  private uploadService = inject(UploadService);
  private imageService = inject(ImageService);
  private awardService = inject(AwardsService);
  private router = inject(Router);
  private messageService = inject(MessageService);
  private modalService = inject(ModalService);
  private headerService = inject(HeaderService);
  private fb = inject(FormBuilder);

  protected baseForm!: FormGroup;
  protected crudMode = signal<crudActionType>(crudActionType.create);
  protected saveButtonTitle = signal<string>('AWARDS.saveAward');
  protected abortButtonTitle = signal<string>('AWARDS.deleteAward');
  protected crudActionType = crudActionType;
  protected isValidForm = signal<boolean>(false);
  protected awardId = signal<string>('');
  protected loading = signal<boolean>(true);
  protected award: Signal<IAward | undefined> = this.awardService.award$;

  protected previewImage: string | undefined = '';
  protected previewVisible = false;

  private initUploadService(): void {
    this.uploadService.FileList = FormUtils.populateImages(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
    this.uploadService.ImageList = FormUtils.populateField(
      this.baseForm,
      ValidatorsField.IMAGES,
    );
  }

  constructor() {
    super();
    this.initForm();
    this.headerService.setCurrentSection(currentSectionType.awardDetail);
    this.route.paramMap
      .pipe(
        takeUntilDestroyed(),
        switchMap((params) => {
          this.awardId.set(<string>params.get('id'));
          return this.route.data.pipe(
            tap((data) => {
              this.crudMode.set(<crudActionType>data['crudType']);
              this.setCrudMode();
            }),
          );
        }),
      )
      .subscribe();
  }

  public get FileList(): NzUploadFile[] {
    return this.uploadService.FileList;
  }

  public set FileList(list: NzUploadFile[]) {
    this.uploadService.FileList = list;
  }

  public get imagesField(): AbstractControl {
    return this.baseForm.get(ValidatorsField.IMAGES)!;
  }

  public get Limits(): IImageSize {
    return IMAGE_LIMITS;
  }

  handlePreview = async (file: NzUploadFile): Promise<void> => {
    if (!file.url && !file['preview']) {
      file['preview'] = await getBase64Async(file.originFileObj!);
    }
    this.previewImage = file.url || file['preview'];
    this.previewVisible = true;
  };

  public removeItem = (file: NzUploadFile): boolean => {
    this.uploadService.removeFiles(file);
    this.imagesField.patchValue(this.uploadService.FileList);
    this.uploadService.FileList.length <= 0 ||
    !this.uploadService.FileList.every((image) => image.error === undefined)
      ? this.isValidForm.set(false)
      : this.isValidForm.set(true);
    return false;
  };

  public showUploadList: ShowUploadListImage = {
    showPreviewIcon: true,
    showRemoveIcon: true,
    hidePreviewIconInNonImage: true,
  };
  public uploadRequest = (item: NzUploadXHRArgs) => {
    return this.imageService.uploadFileLocal(item, IMAGE_LIMITS).subscribe({
      next: (image) => {
        log('UPLAOD OK', image);
        of(image)
          .pipe(delay(200))
          .subscribe(() => {
            this.imagesField.patchValue(this.uploadService.FileList);
          });
      },
      error: (err) => {
        log('ERROR IMAGE', err);
      },
      complete: () => {
        log('IMAEG FILE :IST', this.uploadService.FileList);
      },
    });
  };

  public setMediaUploadHeaders = () => {
    return {
      'Content-Type': 'multipart/form-data',
      Accept: 'application/json',
    };
  };

  private initForm() {
    this.baseForm = this.fb.group({
      id: ['', [Validators.nullValidator]],
      year: [
        '',
        [Validators.required, Validators.minLength(4), Validators.maxLength(4)],
      ],
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(200),
        ],
      ],
      images: ['', [Validators.required, ValidateImages(1, 5)]],
      awards: this.fb.array([]),
    });
  }

  observeFormChanges() {
    this.baseForm.valueChanges.pipe(takeUntilDestroyed()).subscribe((value) => {
      this.isValidForm.set(this.baseForm.valid);
    });
  }

  private fillFormData() {
    FormUtils.fillUpdateDataNestedForm(this.baseForm, this.award()!);
  }

  override setCreateMode() {
    this.baseForm.reset();
    this.initForm();
    this.awardService.setAward(undefined);
    this.saveButtonTitle.set('AWARDS.saveAward');
    this.abortButtonTitle.set('abort');
    this.loading.set(false);
  }

  override setUpdateMode() {
    this.saveButtonTitle.set('AWARDS.updateAward');
    this.abortButtonTitle.set('AWARDS.deleteAward');
    this.awardService.readOne(this.awardId()).subscribe({
      next: (res) => {
        this.fillFormData();
        this.initUploadService();
        this.loading.set(false);
      },
      error: () => {
        this.router.navigateByUrl('/awards');
      },
    });
  }

  ngOnInit(): void {
    this.observeFormChanges();
  }

  override onDataSubmit() {
    this.loading.set(true);
    const award = this.baseForm.value;
    FormUtils.removeObjectNullProperties(award);
    this.messageService.addLoadingMessage('loading');
    this.awardService.create(award).subscribe({
      next: (res) => {
        this.messageService.addSuccessMessage('AWARDS.createSuccess');
        this.loading.set(false);
        this.router.navigate(['awards', res.data!.id]);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  onDataUpdate() {
    this.loading.set(true);
    const award = this.baseForm.value;
    this.messageService.addLoadingMessage('loading');
    this.awardService.update(this.awardId(), award).subscribe({
      next: () => {
        this.messageService.addSuccessMessage('AWARDS.updateSuccess');
        this.loading.set(false);
      },
      error: () => {
        this.loading.set(false);
      },
    });
  }

  onAbortClick() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.loading.set(false);
            this.router.navigateByUrl('/awards');
          },
          title: 'AWARDS.confirmDeleteTitle',
          subtitle: 'AWARDS.confirmDeleteSubtitle',
        });
        break;

      case crudActionType.update:
        this.modalService.confirmDelete({
          confirmFn: () => {
            this.messageService.addLoadingMessage();
            this.awardService.delete(this.awardId()).subscribe({
              next: () => {
                this.loading.set(false);
                this.router.navigateByUrl('/awards');
                this.messageService.addSuccessMessage('AWARDS.deleteSuccess');
              },
            });
          },
          title: 'AWARDS.confirmDeleteTitle',
          subtitle: 'AWARDS.confirmDeleteSubtitle',
        });
        break;
    }
  }

  override setCrudMode() {
    switch (this.crudMode()) {
      case crudActionType.create:
        this.setCreateMode();
        break;
      case crudActionType.update:
        this.setUpdateMode();
        break;
    }
  }
}
