<!-- MAIN CONTAINER -->
<div nz-row #target class="h-100">
  <div nz-col nzSpan="24" class="h-100">
    <div class="container">
      <div class="form">
        <!-- LOADING SPINNER -->
        <nz-spin [nzSpinning]="loading()">
          <!-- AWARD FORM -->
          <form nz-form [formGroup]="baseForm()" nzLayout="vertical">
            <div nz-row [nzGutter]="16">
              <!-- LEFT COLUMN - MAIN AWARD INFO -->
              <div nz-col [nzSpan]="8">
                <!-- AWARD YEAR -->
                <div nz-row [nzGutter]="16">
                  <div nz-col [nzSpan]="16">
                    <app-input-generic
                      [parentForm]="baseForm()"
                      [controlName]="'year'"
                      [label]="'AWARDS.year' | translate"
                      [placeholder]="'AWARDS.yearPlaceholder' | translate"
                      [labelPosition]="'top'"
                    ></app-input-generic>
                  </div>
                </div>
                <!-- AWARD IMAGE -->
                <div nz-row [nzAlign]="'top'">
                  <div nz-row class="w-100" [nzGutter]="16">
                    <div nz-col [nzSpan]="12">
                      <!-- AWARD IMAGE UPLOAD -->
                      <nz-form-item [formGroup]="baseForm()">
                        <nz-form-control>
                          <div class="clearfix">
                            <!-- IMAGE UPLOAD LABEL WITH INFO -->
                            <nz-form-label [nzRequired]="false">
                              <i
                                nz-icon
                                class="info-icon"
                                nz-popover
                                [nzPopoverTitle]="'limits.limit' | translate"
                                [nzPopoverContent]="tplImageInfo"
                                nzType="info-circle"
                              ></i>
                              <strong class="info-image">{{
                                "image" | translate
                              }}</strong>
                              <!-- IMAGE LIMITS INFO TEMPLATE -->
                              <ng-template #tplImageInfo>
                                <div class="info-image">
                                  <strong
                                    >{{ "limits.width" | translate }}:
                                  </strong>
                                  <div class="min-max">
                                    <span>min {{ Limits.width.min }}px</span>
                                    <span>max {{ Limits.width.max }}px</span>
                                  </div>
                                  <strong
                                    >{{ "limits.heigth" | translate }}:
                                  </strong>
                                  <div class="min-max">
                                    <span>min {{ Limits.height.min }}px</span>
                                    <span>max {{ Limits.height.max }}px</span>
                                  </div>
                                </div>
                              </ng-template>
                            </nz-form-label>
                            <!-- IMAGE UPLOAD COMPONENT -->
                            <nz-upload
                              [nzAction]="'http://localhost:4201/api/fakeImage'"
                              [nzCustomRequest]="uploadRequest"
                              [(nzFileList)]="FileList"
                              [nzShowButton]="FileList.length < 1"
                              [nzFileType]="
                                'image/png,image/jpeg,image/gif,image/bmp,image/jpg,image/webp'
                              "
                              [nzHeaders]="setMediaUploadHeaders"
                              [nzPreview]="handlePreview"
                              [nzRemove]="removeItem"
                              [nzShowUploadList]="showUploadList"
                              nzListType="picture-card"
                            >
                              <div>
                                <i nz-icon nzType="client-ui:plus"></i>
                                <div>Carica</div>
                              </div>
                            </nz-upload>
                            <!-- IMAGE PREVIEW MODAL -->
                            <nz-modal
                              [nzVisible]="previewVisible"
                              [nzContent]="modalContent"
                              [nzFooter]="null"
                              (nzOnCancel)="previewVisible = false"
                            >
                              <ng-template #modalContent>
                                <img
                                  [src]="previewImage"
                                  [style]="{ width: '100%' }"
                                />
                              </ng-template>
                            </nz-modal>
                          </div>
                        </nz-form-control>
                      </nz-form-item>
                    </div>
                  </div>
                </div>
              </div>
              <!-- RIGHT COLUMN - AWARD INFO -->
              <div nz-col [nzSpan]="16">
                <!-- AWARD NAME -->
                <div nz-row [nzGutter]="16">
                  <div nz-col [nzSpan]="24">
                    <app-input-generic
                      [parentForm]="baseForm()"
                      [controlName]="'name'"
                      [label]="'AWARDS.name' | translate"
                      [placeholder]="'AWARDS.namePlaceholder' | translate"
                      [labelPosition]="'top'"
                    ></app-input-generic>
                  </div>
                </div>

                <!-- AWARDS -->
                <div nz-row [nzGutter]="16">
                  <div nz-col [nzSpan]="24" formArrayName="awards">
                    @for (
                      award of awardsArray().controls;
                      track award.value.id;
                      let i = $index
                    ) {
                      <div nz-row [nzGutter]="16">
                        <div nz-col [nzSpan]="24">
                          <div
                            nz-row
                            [nzGutter]="16"
                            [formGroupName]="i"
                            [nzAlign]="'bottom'"
                          >
                            <!-- LEFT SIDE - TAKES ALL REMAINING SPACE -->
                            <div nz-col [nzFlex]="'auto'">
                              <div nz-row [nzGutter]="16">
                                <!-- AWARD NAME -->
                                <div nz-col [nzSpan]="12">
                                  <app-input-generic
                                    [parentForm]="award"
                                    [controlName]="'recognition'"
                                    [label]="'AWARDS.recognition' | translate"
                                    [placeholder]="
                                      'AWARDS.recognitionPlaceholder'
                                        | translate
                                    "
                                    [labelPosition]="'top'"
                                  ></app-input-generic>
                                </div>
                                <!-- AWARD PRODUCT -->
                                <div nz-col [nzSpan]="12">
                                  <app-input-select-product
                                    [parentForm]="award"
                                    [controlName]="'product'"
                                    [label]="'AWARDS.product' | translate"
                                    [placeholder]="
                                      'AWARDS.productPlaceholder' | translate
                                    "
                                    [labelPosition]="'top'"
                                    [style]="{ marginBottom: '24px' }"
                                  ></app-input-select-product>
                                </div>
                              </div>
                            </div>
                            <!-- RIGHT SIDE - FIXED 94px WIDTH -->
                            <div nz-col [nzFlex]="'94px'">
                              <div nz-row [nzGutter]="16">
                                @if (i > 0) {
                                  <div nz-col [nzSpan]="12">
                                    <app-simple-button
                                      [iconOnly]="true"
                                      [icon]="'delete'"
                                      (onButtonClick)="removeAward(i)"
                                      [style]="{ marginBottom: '24px' }"
                                    ></app-simple-button>
                                  </div>
                                }
                                @if (
                                  awardsArray().length < 1 ||
                                  i === awardsArray().length - 1
                                ) {
                                  <div nz-col [nzSpan]="12">
                                    <app-simple-button
                                      [autoMinify]="false"
                                      [iconOnly]="true"
                                      [icon]="'plus'"
                                      (onButtonClick)="addAward()"
                                      [style]="{ marginBottom: '24px' }"
                                    ></app-simple-button>
                                  </div>
                                }
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    }

                    <div nz-row [nzGutter]="[16, 16]">
                      <div nz-col nzMd="18" nzLg="12">
                        <app-simple-button
                          [autoMinify]="false"
                          [title]="'AWARDS.save' | translate"
                          [icon]="'plus'"
                          [disabled]="!baseForm().valid"
                          (onButtonClick)="addAward()"
                          [style]="{
                            width: '220px',
                            marginTop: '2px',
                            fontWeight: '700',
                          }"
                        ></app-simple-button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </nz-spin>
      </div>
    </div>
  </div>
</div>
