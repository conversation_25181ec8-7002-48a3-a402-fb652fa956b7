<nz-form-item [formGroup]="parentForm()" [ngStyle]="getCombinedStyles()">
  @if (!!label) {
    <nz-form-label [nzRequired]="isRequired()" nzNoColon="true">
      {{ label | translate }}
    </nz-form-label>
  }
  <nz-form-control nzHasFeedback [nzErrorTip]="showErrorText() && errorTpl">
    <nz-input-group
      [nzPrefixIcon]="prefixIcon()"
      [nzSuffixIcon]="suffixIcon()"
      [nzAddOnBefore]="prefixAddonText()"
      [nzAddOnAfter]="suffixAddonText()"
    >
      <input
        nz-input
        [formControlName]="controlName()"
        [placeholder]="placeholder() | translate"
        [pattern]="pattern()"
        [minlength]="minLength()"
        [maxlength]="maxLength()"
        [type]="type()"
      />
    </nz-input-group>
    <ng-template #errorTpl let-control>
      @if (
        control.touched &&
        control.value?.length > 0 &&
        control.hasError("required")
      ) {
        {{ "INPUTS.required" | translate }}
      }
      @if (
        control.touched &&
        control.value?.length > 0 &&
        control.hasError("pattern")
      ) {
        {{ "INPUTS.notValid" | translate }}
      }
      @if (control.touched && control.hasError("minlength")) {
        {{
          ("INPUTS.insertAtLeast" | translate) +
            " " +
            minLength() +
            " " +
            ("INPUTS.chars" | translate)
        }}
      }
      @if (
        control.touched &&
        control.value?.length > 0 &&
        control.hasError("maxlength")
      ) {
        {{ "INPUTS.maxLengthExceeded" | translate }}
      }
      @for (error of customErrors(); track error) {
        @if (
          control.touched &&
          control.value?.length > 0 &&
          control.hasError(error)
        ) {
          {{ "INPUTS." + error | translate }}
        }
      }
    </ng-template>
  </nz-form-control>
</nz-form-item>
